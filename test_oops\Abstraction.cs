﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;

namespace Action
{
   public abstract class Abstraction 
    {
        public abstract void draw();
    }
    public class triangle : Abstraction
    {
        public static readonly ILog _logger = LogManager.GetLogger(typeof(triangle));
        public override void draw()
        {
            _logger.Info("Drawing a triangle.");

            Console.WriteLine("Triangle");
        }
    }
}
