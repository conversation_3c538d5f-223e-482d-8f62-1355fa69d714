﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;


namespace stendent_details
{
  public class Stdent_details
    {
        private int student_id;
        private string student_Name;
        private int student_age;
        private static readonly ILog _logger = LogManager.GetLogger(typeof(Stdent_details));

        public int stud
        {
            get
            {
                return student_id;
               
            }
            set
            {
                student_id = value;
            }
           
        }
        public string studname
        {
            get
            {
                return student_Name;
            }
            set
            {
                student_Name = value;
            }
        }
        public int studage
        {
            get
            {
                return studage;
            }
            set
            {
                 student_age = value;
            }
        }
        public Stdent_details(int stid,string stdname,int studage)
        {
            student_id = stid;
            student_Name = stdname;
            student_age = studage;
            _logger.Info("All the student detail records are given : " + "student_id :" + " " + student_id + " student Name :" + student_Name + " " + "student_age :" + student_age);
            Console.WriteLine("student_id : " + " " + student_id + " student Name :" + student_Name + " " + "student_age :" + student_age);
            _logger.Info("Values fetched successfully");
            Console.ReadLine();
            
        }
        //private void values(int id,string name,int age)
        //{
        //    // id=Convert.ToInt32( Console.ReadLine());
            
        //    Console.WriteLine("stdent_id : " + id + " student Name :" +name + "student_age :" + age);
        //    Console.ReadLine();
        //}
    }
}
