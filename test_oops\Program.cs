﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using stendent_details;
using Data;
using Test;
using Action;
using log4net;

namespace test_oops
{
    public class student
    {
        private static readonly ILog _logger = LogManager.GetLogger(typeof(student));
        public static void Main()
        {
            try
            {

                Console.WriteLine("Enter the student ID :");
                int id = Convert.ToInt32(Console.ReadLine());
                _logger.Info("Id given");

                Console.WriteLine("Enter the student Name :");
                string name = Console.ReadLine();
                _logger.Info("Name given");

                Console.WriteLine("Enter the student age :");
                int age = Convert.ToInt32(Console.ReadLine());
                _logger.Info("Age given");



                Stdent_details val = new Stdent_details(id, name, age);
                val.stud = id;
                val.studname = name;
                val.studage = age;
                Console.WriteLine("Enter the student ID : " + val.stud + " Enter the student Name : " + val.studname + "Enter the student age :" + val.studage);
                //val.student_id =Convert.ToInt32( Console.ReadLine());
                //val.student_Name = Console.ReadLine();
                _logger.Info("Enter the student ID: " + val.stud + " " + " Enter the student Name: " + val.studname + " " + "Enter the student age: " + val.studage);

                Console.ReadLine();
                _logger.Info("Method Completed");
                Console.ReadKey();

            }
            catch (Exception ex)
            {
                throw;
            }
    }

        //class program
        //{
        //    static void Main(string[] args)
        //    {
        //        _logger.Info("Program started.");

        //        simple s = new simple();
        //        model m = new model();
        //        m.name();
        //        m.age();
        //        _logger.Info("Program execution completed.");

        //        Console.ReadLine();
        //    }
        //}

        // class program
        //{
        //    static void Main(string[] args)
        //    {
        //        Polymorphism obj = new Polymorphism();
        //        Console.WriteLine("The Adding is :"  + obj.add(3, 4, 9));
        //    }
        //}

        //class program
        //{
        //    static void Main(string[] args)
        //    {
        //        Polymorphism obj1 = new Polymorphism();
        //        obj1.job("ID");

        //        morphism obj2 = new morphism();
        //        obj2.job("Police");
        //    }
        //}

        //class Program
        //{
        //    static void Main(string[] args)
        //    {
        //        Abstraction shape = new triangle();
        //        shape.draw();  // Output: Triangle

        //        Console.ReadLine();
        //    }
    }
}
