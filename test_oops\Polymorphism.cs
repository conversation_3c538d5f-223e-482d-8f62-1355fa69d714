﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;

namespace Test
{
    //class Polymorphism
    //{
    //    public static readonly ILog _logger = LogManager.GetLogger(typeof(Polymorphism));
    //    public int add(int a, int b)
    //    {
    //        return a + b;
    //    }
    //    public int add(int a, int b, int c)
    //    {
    //        return a + b + c;
    //    }

    //    public int add(int a, int b, int c, int d)
    //    {
    //        return a + b + c + d;
    //    }
    //}

    public class Polymorphism
    {
        public static readonly ILog _logger = LogManager.GetLogger(typeof(Polymorphism));
        public  virtual void job(string job)
        {
           
            Console.WriteLine("The Parent Primary job is :" + job);

            _logger.Info("The Parent Primary job is :" + job);

        }
    }

    public class morphism  : Polymorphism
    {
        public static readonly ILog _logger = LogManager.GetLogger(typeof(morphism));
        public override void job(string job)
        {
            Console.WriteLine("The Parent Primary job is :" + job);

            _logger.Info("The Parent Primary job is :" + job);
        }

    }
}
