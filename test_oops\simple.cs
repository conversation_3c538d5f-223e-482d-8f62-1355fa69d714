﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using log4net;

namespace Data
{
    class simple
    {
        public static readonly ILog _logger = LogManager.GetLogger(typeof(simple));
        public void name()
        {
            _logger.Info("Into the Name Method");

            Console.WriteLine(" Name : Manikandan.R ");
        }
    }
    class model : simple
    {
        public void age()
        {
            _logger.Info("Into the Age Method");

            Console.WriteLine(" age: 24 ");
        }
    }
}
